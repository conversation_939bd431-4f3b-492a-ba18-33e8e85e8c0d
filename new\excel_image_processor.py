import os
import re
import sys
from openpyxl import load_workbook, Workbook
from openpyxl.utils import get_column_letter
from PIL import Image
import io

def format_style_number(style_no):
    """
    Format style number for valid filename
    Examples:
    BO6004-(HRT 6*6) -> BO6004-HRT_6x6
    BO6833-RD6 (BLUE CR. OPAL) -> BO6833-RD6_BLUE_CR_OPAL
    """
    if not style_no:
        return ""

    # Convert to string and strip whitespace
    formatted = str(style_no).strip()

    # Extract content from parentheses and format it
    parentheses_match = re.search(r'\(([^)]+)\)', formatted)
    if parentheses_match:
        parentheses_content = parentheses_match.group(1)
        # Replace spaces with underscores, * with x, remove dots and other special chars
        formatted_content = re.sub(r'[.\s]+', '_', parentheses_content)
        formatted_content = formatted_content.replace('*', 'x')
        formatted_content = re.sub(r'[^A-Za-z0-9_x-]', '_', formatted_content)
        formatted_content = re.sub(r'_+', '_', formatted_content).strip('_')

        # Remove the original parentheses part and add formatted version
        base_part = re.sub(r'\s*\([^)]*\)', '', formatted).strip()

        # Handle case where base_part ends with dash and we don't want double separator
        if formatted_content:
            if base_part.endswith('-'):
                formatted = f"{base_part}{formatted_content}"
            else:
                formatted = f"{base_part}_{formatted_content}"
        else:
            formatted = base_part

    # Replace any remaining invalid filename characters
    formatted = re.sub(r'[<>:"/\\|?*]', '_', formatted)
    formatted = re.sub(r'_+', '_', formatted).strip('_')

    return formatted

def extract_gram_value(gram_text):
    """
    Extract gram value from text like "14K: 1.05g"
    Returns just the gram value like "1.05g"
    """
    if not gram_text:
        return ""
    
    gram_str = str(gram_text).strip()
    
    # Look for pattern like "14K: 1.05g" and extract just the gram part
    gram_match = re.search(r'(\d+\.?\d*)\s*g(?:ram)?s?', gram_str, re.IGNORECASE)
    if gram_match:
        return f"{gram_match.group(1)}g"
    
    # If it's already just a number with g, return as is
    if re.match(r'^\d+\.?\d*g$', gram_str, re.IGNORECASE):
        return gram_str
    
    return gram_str

def find_style_and_gram_for_image(ws, image_cell, max_search_rows=10):
    """
    Search for style number and gram in cells below the image cell
    Returns tuple (style_number, gram) - both can be None if not found
    """
    col_letter = ''.join(filter(str.isalpha, image_cell))
    row_number = int(''.join(filter(str.isdigit, image_cell)))

    found_values = []

    # Search in cells below the image
    for offset in range(1, max_search_rows + 1):
        search_cell = f"{col_letter}{row_number + offset}"
        cell_value = ws[search_cell].value
        if cell_value and str(cell_value).strip():
            found_values.append(str(cell_value).strip())

    # Try to identify style number and gram from found values
    style_number = None
    gram = None

    for value in found_values:
        # Check if value looks like a style number (contains letters and numbers)
        if re.search(r'[A-Za-z].*\d|[A-Za-z].*[A-Za-z]', value) and not style_number:
            style_number = value
        # Check if value looks like gram (contains 'g' or 'gram' or numbers with colon)
        elif (re.search(r'\d+\s*g(?:ram)?s?$', value.lower()) or 
              re.search(r'\d+K?\s*:\s*\d+\.?\d*g?', value) or
              (value.replace('.', '').isdigit() and not style_number)) and not gram:
            gram = value

    return style_number, gram

def process_excel_file(input_excel_path):
    """
    Process Excel file and extract images with formatted names and create mapping
    """
    # Shopify CDN configuration
    SHOP_ID = "0706/6071/8813"

    # Get base filename without extension
    base_filename = os.path.splitext(os.path.basename(input_excel_path))[0]

    # Create output paths based on input filename
    output_folder = f"{base_filename}_images"
    output_excel = f"{base_filename}_gram_map_data.xlsx"

    # === SETUP OUTPUT FOLDER ===
    os.makedirs(output_folder, exist_ok=True)
    
    # === LOAD THE WORKBOOK ===
    print(f"Loading Excel file: {input_excel_path}")
    try:
        wb = load_workbook(input_excel_path)
        ws = wb.active
    except Exception as e:
        print(f"Error loading Excel file: {e}")
        return None, None, 0

    # === MAP IMAGES TO ANCHOR CELLS ===
    print("Mapping images to cells...")
    image_map = {}
    for image in ws._images:
        # Get cell anchor for each image
        anchor = image.anchor._from
        col_letter = get_column_letter(anchor.col + 1)
        row_number = anchor.row + 1
        cell = f"{col_letter}{row_number}"
        image_map[cell] = image

    print(f"Found {len(image_map)} images in the Excel file")

    # === PROCESS IMAGES AND EXTRACT STYLE NUMBERS ===
    processed_data = []
    saved_count = 0

    for image_cell, image_obj in image_map.items():
        print(f"Processing image in cell {image_cell}...")

        # Find style number and gram in cells below the image
        raw_style_no, raw_gram = find_style_and_gram_for_image(ws, image_cell)

        if raw_style_no:
            # Format the style number for filename
            formatted_style_no = format_style_number(raw_style_no)
            
            # Extract gram value
            extracted_gram = extract_gram_value(raw_gram) if raw_gram else ""

            if formatted_style_no:
                # Determine color based on style number
                if "WG" in formatted_style_no.upper():
                    color = "White"
                elif "PG" in formatted_style_no.upper():
                    color = "Pink"
                else:
                    color = "Yellow"

                # Print information
                print(f"  Original Style: {raw_style_no}")
                print(f"  Formatted Style: {formatted_style_no}")
                print(f"  Color: {color}")
                print(f"  Gram: {extracted_gram if extracted_gram else 'Not found'}")

                try:
                    # Save image with formatted style number as filename
                    img_bytes = image_obj._data()
                    image = Image.open(io.BytesIO(img_bytes))

                    # Use formatted style number as filename
                    image_path = os.path.join(output_folder, f"{formatted_style_no}.png")

                    image.save(image_path)
                    print(f"  Saved: {image_path}")

                    # Create Shopify CDN URL
                    img_src = f"https://cdn.shopify.com/s/files/1/{SHOP_ID}/files/{formatted_style_no}.png"

                    # Store data for Excel output
                    processed_data.append({
                        'cell': image_cell,
                        'original_style': raw_style_no,
                        'formatted_style': formatted_style_no,
                        'color': color,
                        'gram': extracted_gram,
                        'filename': f"{formatted_style_no}.png",
                        'img_src': img_src
                    })

                    saved_count += 1

                except Exception as e:
                    print(f"  Error saving image: {e}")
            else:
                print(f"  No valid style number found after formatting: {raw_style_no}")
        else:
            print(f"  No style number found below image in {image_cell}")

    # === WRITE OUTPUT EXCEL ===
    print("\nCreating output Excel file...")
    wb_out = Workbook()
    ws_out = wb_out.active
    ws_out.title = "Gram Map Data"

    # Add headers - Original Style No, Formatted Style No, Color, Gram, Img Src
    headers = ["Original Style No", "Formatted Style No", "Color", "Gram", "Img Src"]
    ws_out.append(headers)

    # Add data
    for data in processed_data:
        ws_out.append([
            data['original_style'],
            data['formatted_style'],
            data['color'],
            data['gram'],
            data['img_src']
        ])

    wb_out.save(output_excel)

    print(f"\n✅ Processing complete!")
    print(f"   - Processed {len(image_map)} images")
    print(f"   - Successfully saved {saved_count} images to '{output_folder}' folder")
    print(f"   - Created mapping file: '{output_excel}'")
    print(f"   - Images saved as PNG with formatted style numbers as filenames")
    
    return output_folder, output_excel, saved_count

# === MAIN EXECUTION ===
if __name__ == "__main__":
    # Check if filename provided as command line argument
    if len(sys.argv) > 1:
        input_excel = sys.argv[1]
    else:
        # Default file or ask user
        input_excel = input("Enter Excel file path (or press Enter for 'CARTILAGES.xlsx'): ").strip()
        if not input_excel:
            input_excel = "14K BODY J.xlsx"
    
    if os.path.exists(input_excel):
        process_excel_file(input_excel)
    else:
        print(f"Error: File '{input_excel}' not found!")
        print("Please provide a valid Excel file path.")
