#!/usr/bin/env python3
"""
Test script for the formatting functions in excel_image_processor.py
"""

from new.excel_image_processor import format_style_number, extract_gram_value

def test_format_style_number():
    """Test the style number formatting function"""
    print("Testing format_style_number function:")
    print("=" * 50)
    
    test_cases = [
        "BO6004-(HRT 6*6)",
        "BO6833-RD6 (BLUE CR. OPAL)",
        "CAI048-14K (MQ8*4M)",
        "TEST-123 (RED STONE)",
        "SIMPLE-STYLE",
        "STYLE (WITH DOTS.)",
        "STYLE (WITH/SLASH)",
        "STYLE (WITH:COLON)",
    ]
    
    for test_case in test_cases:
        formatted = format_style_number(test_case)
        print(f"'{test_case}' -> '{formatted}'")
    
    print()

def test_extract_gram_value():
    """Test the gram value extraction function"""
    print("Testing extract_gram_value function:")
    print("=" * 50)

    test_cases = [
        # Original test cases
        "14K: 1.05g",
        "18K: 2.5g",
        "1.25g",
        "3.0 grams",
        "14K: 0.75g",
        "22K: 4.2g",
        "2.5",
        "1.05g",
        "Some text with 1.5g in it",
        "No gram value here",

        # New test cases for the updated requirements
        "8.60GM",
        "14K - 4.70 g",
        "5.55 g",
        "14K(5.02g)",
        "3.90GM",
        "10K - 2.30 g",
        "18K(3.45g)",
        "7.25 GM",
        "14K - 1.80 gm",
        "22K(4.60GM)",
    ]

    for test_case in test_cases:
        extracted = extract_gram_value(test_case)
        print(f"'{test_case}' -> '{extracted}'")

    print()

def test_img_src_generation():
    """Test the image source URL generation"""
    print("Testing Img Src URL generation:")
    print("=" * 50)

    SHOP_ID = "0706/6071/8813"

    test_cases = [
        "BO6004-HRT_6x6",
        "BO6833-RD6_BLUE_CR_OPAL",
        "CAI048-14K_MQ8x4M",
    ]

    for formatted_name in test_cases:
        img_src = f"https://cdn.shopify.com/s/files/1/{SHOP_ID}/files/{formatted_name}.png"
        print(f"'{formatted_name}' -> '{img_src}'")

    print()

if __name__ == "__main__":
    test_format_style_number()
    test_extract_gram_value()
    test_img_src_generation()

    print("Expected outputs:")
    print("BO6004-(HRT 6*6) should become: BO6004-HRT_6x6")
    print("BO6833-RD6 (BLUE CR. OPAL) should become: BO6833-RD6_BLUE_CR_OPAL")
    print("14K: 1.05g should extract: 1.05g")
    print("Img Src should be: https://cdn.shopify.com/s/files/1/0706/6071/8813/files/{formatted_name}.png")
