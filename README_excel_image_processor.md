# Excel Image Processor

This program reads an Excel file, extracts embedded images, and saves them with formatted style numbers as filenames. It also creates a mapping file with original style numbers, formatted style numbers, colors, and gram values.

## Features

- **Style Number Formatting**: Converts style numbers to valid filenames
  - `BO6004-(HRT 6*6)` → `BO6004-HRT_6x6`
  - `BO6833-RD6 (BLUE CR. OPAL)` → `BO6833-RD6_BLUE_CR_OPAL`

- **Gram Value Extraction**: Extracts gram values from text
  - `14K: 1.05g` → `1.05g`
  - `18K: 2.5g` → `2.5g`

- **Color Detection**: Determines color based on style number
  - `WG` → White
  - `PG` → Pink
  - Default → Yellow

- **Output Organization**: Creates organized output based on input filename
  - Images folder: `{input_filename}_images`
  - Mapping file: `{input_filename}_gram_map_data.xlsx`

## Requirements

Install the required packages:

```bash
pip install openpyxl pillow
```

## Usage

### Method 1: Command Line with Filename
```bash
python excel_image_processor.py your_excel_file.xlsx
```

### Method 2: Interactive Mode
```bash
python excel_image_processor.py
```
Then enter the Excel file path when prompted.

### Method 3: Edit the Script
Edit the `input_excel` variable in the script:
```python
input_excel = "your_excel_file.xlsx"
```

## Output

For an input file named `CARTILAGES.xlsx`, the program creates:

1. **Images Folder**: `CARTILAGES_images/`
   - Contains PNG images named with formatted style numbers
   - Example: `BO6004-HRT_6x6.png`

2. **Mapping File**: `CARTILAGES_gram_map_data.xlsx`
   - Contains columns: Original Style No, Formatted Style No, Color, Gram
   - Maps original data to formatted data for reference

## Example Output

```
Loading Excel file: CARTILAGES.xlsx
Mapping images to cells...
Found 25 images in the Excel file
Processing image in cell B2...
  Original Style: BO6004-(HRT 6*6)
  Formatted Style: BO6004-HRT_6x6
  Color: Yellow
  Gram: 1.05g
  Saved: CARTILAGES_images/BO6004-HRT_6x6.png

✅ Processing complete!
   - Processed 25 images
   - Successfully saved 23 images to 'CARTILAGES_images' folder
   - Created mapping file: 'CARTILAGES_gram_map_data.xlsx'
   - Images saved as PNG with formatted style numbers as filenames
```

## How It Works

1. **Image Detection**: Scans the Excel file for embedded images
2. **Data Extraction**: Searches cells below each image for style numbers and gram values
3. **Formatting**: Applies formatting rules to create valid filenames
4. **Color Assignment**: Determines color based on style number patterns
5. **File Saving**: Saves images with formatted names and creates mapping file

## Formatting Rules

### Style Number Formatting
- Removes parentheses and formats content inside them
- Replaces spaces with underscores
- Replaces `*` with `x`
- Removes invalid filename characters
- Handles special cases like dashes before parentheses

### Gram Value Extraction
- Extracts numeric values followed by 'g' or 'gram'
- Handles patterns like "14K: 1.05g"
- Returns just the gram portion (e.g., "1.05g")

## Testing

Run the test script to verify formatting functions:
```bash
python test_format_functions.py
```

This will show examples of how style numbers and gram values are formatted.
